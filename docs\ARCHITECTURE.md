# LLMLog Architecture Documentation

**Version:** 0.1.0  
**Date:** August 17, 2025  
**Last Updated:** August 17, 2025

## Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Component Design](#component-design)
4. [Data Flow](#data-flow)
5. [Security Architecture](#security-architecture)
6. [Performance Architecture](#performance-architecture)
7. [Extension Lifecycle](#extension-lifecycle)
8. [Design Patterns](#design-patterns)
9. [Scalability Considerations](#scalability-considerations)
10. [Architecture Decision Records](#architecture-decision-records)

## Overview

LLMLog is a Chrome extension built using Manifest V3 architecture that automatically captures, stores, and manages conversations from major LLM platforms. The architecture emphasizes security, performance, modularity, and maintainability.

### Core Principles

- **Security First**: All operations are performed locally with no external data transmission
- **Performance Optimized**: Advanced caching, indexing, and async operations
- **Modular Design**: Clear separation of concerns with well-defined interfaces
- **Extensible**: Easy to add new platforms and features
- **Robust Error Handling**: Graceful degradation and comprehensive error recovery

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Web Pages"
        A[ChatGPT]
        B[Claude]
        C[Gemini]
        D[Other LLMs]
    end
    
    subgraph "Chrome Extension"
        E[Content Scripts]
        F[Service Worker]
        G[Popup UI]
        H[Options Page]
    end
    
    subgraph "Core Modules"
        I[Router]
        J[Storage]
        K[Capture]
        L[Logger]
        M[Settings]
    end
    
    subgraph "Performance Layer"
        N[Performance Monitor]
        O[Search Optimizer]
        P[Cache Manager]
        Q[DOM Optimizer]
    end
    
    subgraph "Data Layer"
        R[IndexedDB]
        S[Chrome Storage]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> I
    G --> F
    H --> F
    
    I --> J
    I --> K
    I --> L
    I --> M
    
    J --> N
    J --> O
    J --> P
    J --> Q
    
    J --> R
    M --> S
```

### Component Layers

#### 1. Presentation Layer
- **Popup UI**: Main user interface for browsing conversations
- **Options Page**: Configuration and settings management
- **Content Scripts**: Injected into web pages for data capture

#### 2. Business Logic Layer
- **Router Module**: Central message routing and coordination
- **Storage Module**: Data persistence and retrieval operations
- **Capture Module**: Platform configuration and conversation parsing
- **Logger Module**: Structured logging and debugging
- **Settings Module**: Configuration management

#### 3. Performance Layer
- **Performance Monitor**: Real-time performance tracking
- **Search Optimizer**: Advanced search algorithms with relevance scoring
- **Cache Manager**: Intelligent caching with LRU eviction
- **DOM Optimizer**: Asynchronous DOM updates and virtual scrolling

#### 4. Data Layer
- **IndexedDB**: Primary data storage for conversations
- **Chrome Storage**: Extension settings and configuration

## Component Design

### Core Modules

#### Storage Module
```javascript
// Responsibilities:
// - Data persistence and retrieval
// - Duplicate detection and prevention
// - Search and filtering operations
// - Database schema management

class StorageModule {
  async saveConversation(data)
  async getConversations(options)
  async deleteConversation(id)
  async getTotalCount(search)
}
```

#### Router Module
```javascript
// Responsibilities:
// - Message routing between components
// - Request/response standardization
// - Error handling and logging
// - Async operation management

class RouterModule {
  handleMessage(message, sender, sendResponse)
  route(namespace, action, payload)
  wrapResponse(handler)
}
```

#### Performance Integration
```javascript
// Responsibilities:
// - Coordinate all performance optimizations
// - Monitor system health
// - Adaptive optimization
// - Resource management

class PerformanceIntegration {
  async initialize(config)
  async performOptimizedSearch(term, data, onProgress)
  getPerformanceStats()
  optimizeConfiguration()
}
```

### Platform Integration

#### Platform Parser Interface
```javascript
// Each platform implements this interface
export const config = {
  name: string,
  apiEndpoint: string,
  duplicateWindow: number
};

export async function parseRequest(request, logger);
export async function parseResponse(response, logger);
```

#### Supported Platforms
- **ChatGPT**: SSE stream parsing with conversation threading
- **Claude**: JSON response parsing with message concatenation
- **Gemini**: Form data parsing with nested JSON structures
- **DeepSeek**: SSE stream parsing with session management
- **Tongyi**: Dialog conversation parsing
- **Kimi**: Chat completion parsing

## Data Flow

### Conversation Capture Flow

```mermaid
sequenceDiagram
    participant WP as Web Page
    participant CS as Content Script
    participant IC as Interceptor
    participant PP as Platform Parser
    participant BR as Bridge
    participant SW as Service Worker
    participant RT as Router
    participant ST as Storage
    participant DB as IndexedDB

    WP->>CS: API Request/Response
    CS->>IC: Intercept Network Traffic
    IC->>PP: Parse Platform Data
    PP->>BR: Structured Conversation
    BR->>SW: Send Message
    SW->>RT: Route Message
    RT->>ST: Save Conversation
    ST->>DB: Store Data
    DB-->>ST: Confirmation
    ST-->>RT: Success Response
    RT-->>SW: Response
    SW-->>BR: Acknowledgment
```

### Search Operation Flow

```mermaid
sequenceDiagram
    participant UI as Popup UI
    participant PI as Performance Integration
    participant SC as Search Cache
    participant SO as Search Optimizer
    participant PS as Progressive Search
    participant ST as Storage
    participant DB as IndexedDB

    UI->>PI: Search Request
    PI->>SC: Check Cache
    alt Cache Hit
        SC-->>PI: Cached Results
        PI-->>UI: Immediate Results
    else Cache Miss
        PI->>PS: Progressive Search
        PS->>SO: Optimize Query
        SO->>ST: Database Query
        ST->>DB: Indexed Search
        DB-->>ST: Raw Results
        ST-->>SO: Data
        SO-->>PS: Scored Results
        PS-->>PI: Progressive Results
        PI->>SC: Cache Results
        PI-->>UI: Final Results
    end
```

## Security Architecture

### Content Security Policy

```javascript
// Extension pages CSP
"content_security_policy": {
  "extension_pages": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'; object-src 'none'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content;"
}
```

### Script Injection Security

```javascript
// Security validation for all injected scripts
class SecurityValidator {
  static async validateScript(scriptUrl) {
    // URL validation
    // Path whitelist checking
    // Size limits
    // Timeout protection
  }
}
```

### Data Protection

- **Local-Only Storage**: No external data transmission
- **Encrypted Storage**: Browser-level IndexedDB encryption
- **Input Sanitization**: XSS protection for all user inputs
- **Permission Minimization**: Least privilege principle

## Performance Architecture

### Optimization Strategies

#### 1. Database Optimization
- **Compound Indexes**: Efficient multi-field queries
- **Pagination**: Limit memory usage with chunked loading
- **Connection Pooling**: Reuse database connections

#### 2. Search Optimization
- **Relevance Scoring**: Weighted field matching with age boost
- **Progressive Loading**: Stream results as they're found
- **Intelligent Caching**: LRU cache with TTL expiration

#### 3. UI Optimization
- **Virtual Scrolling**: Render only visible items
- **Async DOM Updates**: Non-blocking UI updates
- **Debounced Input**: Reduce excessive API calls

#### 4. Memory Management
- **Bounded Caches**: Automatic size limits and cleanup
- **Event Listener Tracking**: Prevent memory leaks
- **Garbage Collection Hints**: Optimize memory usage

### Performance Monitoring

```javascript
// Real-time performance tracking
const metrics = {
  searchOperations: [],     // Search timing and results
  domUpdates: [],          // DOM operation performance
  memoryUsage: [],         // JavaScript heap monitoring
  frameRates: [],          // Rendering performance
  cacheHits: number,       // Cache efficiency
  cacheMisses: number
};
```

## Extension Lifecycle

### Initialization Sequence

1. **Service Worker Startup**
   - Load core modules
   - Initialize router
   - Setup keep-alive mechanism
   - Start memory monitoring

2. **Content Script Injection**
   - Validate target platform
   - Inject interceptor scripts
   - Setup communication bridge
   - Begin conversation monitoring

3. **Popup Activation**
   - Initialize performance integration
   - Load conversation data
   - Setup virtual scrolling
   - Enable search functionality

### Shutdown and Cleanup

1. **Service Worker Suspension**
   - Save pending operations
   - Clear temporary data
   - Disconnect ports
   - Trigger garbage collection

2. **Content Script Cleanup**
   - Remove event listeners
   - Clear interceptors
   - Disconnect bridges
   - Release resources

## Design Patterns

### 1. Repository Pattern
```javascript
// Storage module acts as repository
class ConversationRepository {
  async save(conversation)
  async findById(id)
  async findBySearch(criteria)
  async delete(id)
}
```

### 2. Observer Pattern
```javascript
// Event-driven communication
window.dispatchEvent(new CustomEvent('llmlog-search-completed', {
  detail: { searchTerm, resultCount, duration }
}));
```

### 3. Factory Pattern
```javascript
// Platform parser creation
function createPlatformParser(platformName) {
  return import(`./platforms/${platformName.toLowerCase()}.js`);
}
```

### 4. Strategy Pattern
```javascript
// Different duplicate detection strategies
const duplicateStrategies = {
  idBased: (conv1, conv2) => conv1.conversationId === conv2.conversationId,
  contentBased: (conv1, conv2) => conv1.prompt === conv2.prompt,
  urlBased: (conv1, conv2) => conv1.url === conv2.url
};
```

### 5. Singleton Pattern
```javascript
// Single instance of performance monitor
export const performanceMonitor = new PerformanceMonitor();
```

## Scalability Considerations

### Data Scalability

- **Pagination**: Handle large datasets efficiently
- **Indexing**: Optimize query performance
- **Archiving**: Future support for data archiving
- **Compression**: Reduce storage footprint

### Feature Scalability

- **Plugin Architecture**: Easy platform addition
- **Configuration System**: Flexible settings management
- **API Versioning**: Backward compatibility
- **Module Loading**: Dynamic feature loading

### Performance Scalability

- **Adaptive Algorithms**: Self-optimizing performance
- **Resource Monitoring**: Automatic resource management
- **Caching Strategies**: Multi-level caching
- **Background Processing**: Non-blocking operations

## Architecture Decision Records

### ADR-001: Manifest V3 Adoption
**Decision**: Use Manifest V3 for the Chrome extension
**Rationale**: Future-proofing, enhanced security, better performance
**Consequences**: Service worker limitations, but improved security model

### ADR-002: IndexedDB for Storage
**Decision**: Use IndexedDB as primary storage mechanism
**Rationale**: Large storage capacity, structured queries, offline support
**Consequences**: More complex than localStorage, but much more capable

### ADR-003: Modular Architecture
**Decision**: Implement modular architecture with clear separation of concerns
**Rationale**: Maintainability, testability, extensibility
**Consequences**: More initial complexity, but better long-term maintainability

### ADR-004: Performance-First Design
**Decision**: Implement comprehensive performance optimization from the start
**Rationale**: Better user experience, scalability, competitive advantage
**Consequences**: Increased complexity, but significantly better performance

### ADR-005: Local-Only Data Processing
**Decision**: Keep all data processing local with no external transmission
**Rationale**: Privacy, security, user trust
**Consequences**: Limited cloud features, but maximum privacy protection

### ADR-006: Progressive Enhancement
**Decision**: Implement fallback mechanisms for all advanced features
**Rationale**: Reliability, graceful degradation, broader compatibility
**Consequences**: More code to maintain, but better reliability

## Future Architecture Considerations

### Planned Enhancements

1. **Web Workers**: Offload heavy processing
2. **Streaming APIs**: Real-time data processing
3. **Machine Learning**: Local conversation analysis
4. **Export/Import**: Data portability features
5. **Collaboration**: Shared conversation features

### Technology Evolution

- **WebAssembly**: Performance-critical operations
- **Service Worker Improvements**: Better background processing
- **IndexedDB Enhancements**: Advanced query capabilities
- **Chrome API Evolution**: New extension capabilities
