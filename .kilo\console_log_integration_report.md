# Console.log 整合到 Logger 模塊報告

## 1. 項目中 console.log 使用情況分析

通過搜索，發現項目中共有 68 個 `console.log` 語句，分布在多個文件中：

### 主要文件分布：

1. **popup.js**：包含多個 `console.log` 語句，用於調試和性能監控
2. **options.js**：包含少量 `console.log` 語句，主要用於錯誤處理
3. **scripts/capture/injector.js**：包含多個 `console.log` 語句，用於安全驗證和調試
4. **scripts/capture/bridge.js**：包含多個 `console.log` 語句，用於消息傳遞和調試
5. **其他模塊文件**：如 `modules/search-cache.js`、`modules/progressive-search.js` 等
6. **測試文件**：包含一些用於測試的 `console.log` 語句

## 2. 現有 Logger 模塊分析

項目已經有一個 logger 模塊 (`modules/logger.js`)，具有以下特點：

- 支持條件性日誌記錄（通過 `debugMode` 參數控制）
- 提供 `log`、`error` 和 `warn` 方法
- 使用 `LLMLog:` 前綴來標識日誌
- 可以通過 `debugMode` 參數啟用或禁用日誌輸出

## 3. Enable Debug Logging 控制機制

在 `options.html` 中有一個 "Enable Debug Logging" 選項，其值存儲在 `chrome.storage.local` 中。相關實現如下：

- 在 `options.js` 中，該選項的值被監聽並保存到存儲中
- 在 `scripts/capture/injector.js` 和 `scripts/capture/bridge.js` 中，通過 `chrome.runtime.sendMessage` 獲取該設置的值
- 在 `service-worker.js` 中，該設置用於初始化 logger

## 4. 整合可行性評估

將所有 `console.log` 語句整合到 logger 模塊是完全可行的，原因如下：

1. 現有的 logger 模塊已經提供了所需的功能
2. 項目已經建立了從 options 頁面控制日誌輸出的機制
3. 大多數需要日誌記錄的模塊都可以訪問 chrome.storage API
4. 對於無法直接訪問 chrome.storage 的上下文（如內容腳本），可以通過消息傳遞機制獲取設置

## 5. 整合方案建議

### 5.1. 修改現有 logger 模塊

增強現有的 logger 模塊以支持動態更新 debugMode：

```javascript
// modules/logger.js
export function createLogger(initialDebugMode) {
    let debugMode = initialDebugMode;
    
    return {
        setDebugMode(mode) {
            debugMode = mode;
        },
        log: (...args) => {
            if (debugMode) {
                console.log('LLMLog:', ...args);
            }
        },
        error: (...args) => {
            // 錯誤日誌通常應該始終輸出，但可以尊重 debugMode 設置
            if (debugMode) {
                console.error('LLMLog:', ...args);
            }
        },
        warn: (...args) => {
            if (debugMode) {
                console.warn('LLMLog:', ...args);
            }
        }
    };
}
```

### 5.2. 在各模塊中使用 logger

將模塊中的 `console.log` 替換為 logger 調用：

```javascript
// 在 popup.js 中
import { createLogger } from './modules/logger.js';

// 初始化 logger（可能需要通過消息傳遞獲取設置）
const logger = createLogger(debugMode);

// 替換 console.log 語句
logger.log('🚀 Advanced performance integration loaded');
```

### 5.3. 在內容腳本中使用 logger

對於內容腳本（如 `scripts/capture/injector.js` 和 `scripts/capture/bridge.js`），需要通過消息傳遞機制獲取設置：

```javascript
// 在 scripts/capture/bridge.js 中
chrome.runtime.sendMessage({
    namespace: 'settings',
    action: 'get',
    payload: { key: 'debugLoggingEnabled' }
}, (debugLoggingEnabled) => {
    // 使用獲取到的設置初始化 logger
    const logger = createLogger(debugLoggingEnabled);
});
```

### 5.4. 在 Service Worker 中使用 logger

在 `service-worker.js` 中，可以直接訪問存儲來獲取設置：

```javascript
// 在 service-worker.js 中
import { getSetting } from './modules/settings.js';
import { createLogger } from './modules/logger.js';

async function main() {
    const debugLoggingEnabled = await getSetting('debugLoggingEnabled');
    const logger = createLogger(debugLoggingEnabled);
}
```

## 6. 實施步驟（逐文件修改）

### 第一階段：核心模塊修改

1. **modules/logger.js** - 增強 logger 模塊功能
2. **service-worker.js** - 修改 Service Worker 中的日誌使用
3. **modules/router.js** - 修改路由模塊中的日誌使用

### 第二階段：內容腳本修改

4. **scripts/capture/bridge.js** - 修改 Bridge 腳本中的日誌使用
5. **scripts/capture/injector.js** - 修改 Injector 腳本中的日誌使用

### 第三階段：用戶界面修改

6. **popup.js** - 修改 Popup 頁面中的日誌使用
7. **options.js** - 修改 Options 頁面中的日誌使用

### 第四階段：其他模塊修改

8. **modules/*.js** - 修改其他模塊文件中的日誌使用
9. **測試文件** - 修改測試文件中的日誌使用（可選）

## 7. 注意事項

1. 需要特別注意錯誤日誌的處理，某些錯誤可能需要始終記錄，即使在 debugMode 為 false 時
2. 在內容腳本中，由於上下文隔離，可能需要在多個地方初始化 logger
3. 需要確保在所有需要日誌記錄的地方都能正確獲取到 debugMode 設置
4. 測試時需要驗證 Enable Debug Logging 選項的切換是否能正確控制日誌輸出
5. 某些性能測量的日誌可能需要特殊處理，因為它們用於性能分析

## 8. 預期效果

完成整合後，將實現以下效果：

1. 所有日誌輸出統一通過 logger 模塊管理
2. 日誌輸出完全受 Enable Debug Logging 選項控制
3. 日誌格式統一，便於識別和過濾
4. 更容易維護和擴展日誌功能
5. 減少直接使用 `console.log` 帶來的潛在問題