
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>LLMLog Modern</title>
  <!-- Local Tailwind CSS -->
  <link href="dist/output.css" rel="stylesheet">
  <!-- Custom Styles -->
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="lib/github-dark.css">
</head>
<body class="bg-slate-100 text-slate-800 font-sans w-[450px] max-h-[580px] flex flex-col">

  <!-- Screen reader skip link -->
  <a href="#conversation-list" class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:px-3 focus:py-1 focus:bg-blue-600 focus:text-white focus:rounded-md focus:z-50">Skip to main content</a>

  <!-- Main Application Container -->
  <div id="app" role="application" aria-label="LLMLog Conversation Manager" class="flex flex-col h-full">

    <!-- =================================================================== -->
    <!-- ========================= LIST VIEW =============================== -->
    <!-- =================================================================== -->
    <div id="list-view" role="main" class="flex flex-col h-full">
      <!-- Header -->
      <header class="bg-white p-4 border-b border-slate-200 shadow-sm z-10">
        <h1 id="app-title" class="text-xl font-bold text-center text-slate-700">LLMLog</h1>
        <div class="relative mt-3">
          <label for="search-input" class="sr-only">Search conversations</label>
          <input
            type="search"
            id="search-input"
            placeholder="Search conversations..."
            aria-describedby="search-help"
            role="searchbox"
            autocomplete="off"
            class="w-full px-4 py-2 text-sm bg-slate-100 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
          >
          <!-- Search loading indicator, absolutely positioned relative to the search bar container -->
          <div id="search-loading-indicator" class="absolute top-full left-0 right-0 bg-white border border-t-0 border-slate-200 rounded-b-lg p-2 text-xs text-slate-500 flex items-center justify-center gap-2 hidden">
            <span class="spinner w-3 h-3 border-2 border-slate-200 border-t-blue-500 rounded-full animate-spin"></span>
            <span>Searching...</span>
          </div>
        </div>
        <div id="search-help" class="sr-only">
          Type to search through your conversation history. Results will appear below.
        </div>
      </header>

      <!-- Conversation List -->
      <main
        id="conversation-list"
        role="list"
        aria-live="polite"
        aria-label="Conversation list"
        tabindex="0"
        class="flex-1 overflow-y-auto p-2 custom-scrollbar bg-slate-50 focus:outline-none"
      >
        <!-- Conversation items will be dynamically inserted here -->
        <div class="empty-state text-center py-10 px-4">
          <p class="empty-message text-slate-500" role="status" aria-live="polite">No conversations recorded yet.</p>
        </div>
      </main>

      <!-- Screen reader status updates -->
      <div id="list-status" class="sr-only" aria-live="polite" aria-atomic="true"></div>
    </div>

    <!-- =================================================================== -->
    <!-- ========================= DETAIL VIEW ============================= -->
    <!-- =================================================================== -->
    <div id="detail-view" class="hidden flex-col h-full fade-in" role="main" aria-labelledby="detail-title">
      <!-- Detail Header -->
      <header class="bg-white p-3 flex items-center gap-3 border-b border-slate-200 shadow-sm z-10 flex-shrink-0">
        <button
          id="back-button"
          class="back-button flex items-center gap-1 px-3 py-1.5 text-sm text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Go back to conversation list"
          type="button"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
          Back
        </button>
        <h1 id="detail-title" class="text-base font-semibold text-slate-700 truncate">Conversation Detail</h1>
      </header>

      <!-- Conversation Detail Content -->
      <main id="conversation-detail" role="article" tabindex="0" class="flex-1 overflow-y-auto p-4 bg-white custom-scrollbar focus:outline-none">
        <!-- Conversation detail will be dynamically inserted here -->
      </main>
    </div>

  </div>

  <!-- External Libraries -->
  <script src="lib/marked.min.js"></script>
  <script src="lib/highlight.min.js"></script>
  <script src="lib/dompurify.min.js"></script>
  
  <!-- Main Application Logic -->
  <!-- We will create new_popup.js next -->
  <script type="module" src="popup.js"></script>
</body>
</html>