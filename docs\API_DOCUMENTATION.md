# LLMLog Chrome Extension - API Documentation

**Version:** 0.1.0  
**Date:** August 17, 2025  
**Last Updated:** August 17, 2025

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Modules](#core-modules)
4. [Message Router API](#message-router-api)
5. [Storage API](#storage-api)
6. [Performance Modules API](#performance-modules-api)
7. [Platform Integration API](#platform-integration-api)
8. [Security API](#security-api)
9. [Error Handling](#error-handling)
10. [Examples](#examples)

## Overview

LLMLog is a Chrome extension that automatically captures, manages, and searches conversations from major LLM platforms. This document provides comprehensive API documentation for developers working with or extending the LLMLog codebase.

### Key Features

- **Automatic Conversation Capture**: Intercepts API responses from 6 major LLM platforms
- **Local Storage**: Uses IndexedDB for secure, local-only data persistence
- **Advanced Search**: Optimized search with relevance scoring and caching
- **Performance Optimization**: Comprehensive performance monitoring and optimization
- **Security**: Content Security Policy enforcement and secure script injection

## Architecture

### Module Structure

```
LLMLog/
├── modules/                    # Core business logic modules
│   ├── storage.js             # Data persistence layer
│   ├── router.js              # Message routing system
│   ├── capture.js             # Platform configuration
│   ├── logger.js              # Logging system
│   ├── settings.js            # Configuration management
│   ├── performance-monitor.js  # Performance tracking
│   ├── search-optimizer.js    # Search optimization
│   ├── async-dom-updater.js   # DOM update optimization
│   ├── search-cache.js        # Search result caching
│   ├── progressive-search.js  # Progressive search loading
│   └── performance-integration.js # Performance integration
├── scripts/capture/           # Content script injection system
│   ├── injector.js           # Secure script injection
│   ├── interceptor.js        # API interception
│   ├── bridge.js             # Communication bridge
│   └── platforms/            # Platform-specific parsers
└── popup.js                  # User interface logic
```

### Communication Flow

```mermaid
graph TD
    A[Content Script] --> B[Interceptor]
    B --> C[Platform Parser]
    C --> D[Bridge]
    D --> E[Service Worker]
    E --> F[Router]
    F --> G[Storage Module]
    G --> H[IndexedDB]
    
    I[Popup] --> E
    E --> J[Performance Monitor]
    E --> K[Search Optimizer]
```

## Core Modules

### Storage Module (`modules/storage.js`)

The storage module provides a clean, promise-based API for all IndexedDB operations.

#### Database Schema

```javascript
// Database configuration
const DB_NAME = 'LLMLogDB';
const DB_VERSION = 2;
const STORE_NAME = 'conversations';

// Conversation object structure
{
  id: number,              // Auto-generated primary key
  platform: string,        // Platform name (e.g., 'ChatGPT', 'Claude')
  title: string,           // Conversation title
  prompt: string,          // User's input/question
  response: string,        // AI's response
  url: string,             // Source URL
  createdAt: number,       // Timestamp
  conversationId: string,  // Platform-specific conversation ID
  messageId: string        // Platform-specific message ID
}
```

#### Indexes

- `createdAt`: For date-based sorting
- `platform`: For platform filtering
- `title`: For title-based searches
- `url`: For duplicate detection
- `platform_createdAt`: Compound index for platform + date queries
- `createdAt_platform`: Compound index for date + platform queries

### Router Module (`modules/router.js`)

The router module acts as a central message hub, routing messages between different parts of the extension.

#### Message Format

```javascript
{
  namespace: string,    // Module namespace (e.g., 'database', 'settings')
  action: string,       // Action to perform (e.g., 'saveConversation')
  payload: object       // Action-specific data
}
```

#### Response Format

```javascript
{
  status: 'success' | 'error',
  data?: any,              // Success data
  message?: string,        // Error message
  details?: any           // Additional error details
}
```

### Logger Module (`modules/logger.js`)

Provides structured logging with configurable levels and session storage.

#### Log Levels

- `info`: General information
- `warn`: Warning messages
- `error`: Error messages
- `debug`: Debug information (when debug mode enabled)

### Settings Module (`modules/settings.js`)

Manages extension configuration using Chrome's storage API.

#### Available Settings

- `debugLoggingEnabled`: Enable/disable debug logging
- `captureEnabled`: Enable/disable conversation capture
- `platformSettings`: Platform-specific configuration

## Message Router API

### Database Operations

#### Save Conversation

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'database',
  action: 'saveConversation',
  payload: {
    platform: 'ChatGPT',
    title: 'Example conversation',
    prompt: 'What is AI?',
    response: 'AI is...',
    url: 'https://chat.openai.com/c/123',
    conversationId: 'conv-123',
    messageId: 'msg-456'
  }
});
```

#### Get Conversations

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'database',
  action: 'getConversations',
  payload: {
    page: 1,
    limit: 50,
    search: 'artificial intelligence',
    platform: 'ChatGPT'
  }
});
```

#### Get All Conversations

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'database',
  action: 'getAllConversations'
});
```

#### Delete Conversation

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'database',
  action: 'deleteConversation',
  payload: { id: 123 }
});
```

#### Get Total Count

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'database',
  action: 'getTotalConversationCount',
  payload: { search: 'optional search term' }
});
```

### Settings Operations

#### Get Setting

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'settings',
  action: 'get',
  payload: { key: 'debugLoggingEnabled' }
});
```

#### Set Setting

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'settings',
  action: 'set',
  payload: { 
    key: 'debugLoggingEnabled', 
    value: true 
  }
});
```

### Logging Operations

#### Add Log Entry

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'logging',
  action: 'addLog',
  payload: {
    level: 'info',
    message: 'User performed search',
    timestamp: Date.now(),
    context: { searchTerm: 'AI' }
  }
});
```

#### Get Logs

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'logging',
  action: 'getLogs'
});
```

#### Clear Logs

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'logging',
  action: 'clearLogs'
});
```

### Capture Operations

#### Get Platform Configuration

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'capture',
  action: 'getPlatformConfig',
  payload: 'ChatGPT'
});
```

### Security Operations

#### Report CSP Violation

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'security',
  action: 'reportCSPViolation',
  payload: {
    violatedDirective: 'script-src',
    blockedURI: 'https://malicious-site.com/script.js',
    documentURI: 'https://chat.openai.com/',
    timestamp: Date.now()
  }
});
```

#### Get CSP Violations

```javascript
const response = await chrome.runtime.sendMessage({
  namespace: 'security',
  action: 'getCSPViolations'
});
```

## Storage API

### Direct Module Usage

When working directly with the storage module (e.g., in tests):

```javascript
import * as storage from './modules/storage.js';

// Save conversation
const result = await storage.saveConversation(conversationData);

// Get conversations with pagination and search
const conversations = await storage.getConversations({
  page: 1,
  limit: 50,
  search: 'search term',
  platform: 'ChatGPT'
});

// Get all conversations
const allConversations = await storage.getAllConversations();

// Delete conversation
const deleteResult = await storage.deleteConversation({ id: 123 });

// Get total count
const countResult = await storage.getTotalConversationCount('search term');
```

### Duplicate Detection

The storage module implements sophisticated duplicate detection:

1. **ID-based**: Checks for existing `conversationId` + `messageId`
2. **Content-based**: Compares URL, prompt, and response content
3. **Time-based**: Considers duplicates within platform-specific time windows

```javascript
// Duplicate detection windows (in milliseconds)
const DUPLICATE_WINDOWS = {
  'ChatGPT': 30000,    // 30 seconds
  'Claude': 15000,     // 15 seconds
  'Gemini': 20000,     // 20 seconds
  'default': 25000     // 25 seconds
};
```

## Performance Modules API

### Performance Monitor

```javascript
import { performanceMonitor } from './modules/performance-monitor.js';

// Initialize monitoring
performanceMonitor.initialize();

// Record search operation
performanceMonitor.recordSearchOperation(duration, resultCount, fromCache);

// Record DOM update
performanceMonitor.recordDOMUpdate(duration, updateType);

// Get statistics
const stats = performanceMonitor.getStatistics();

// Export data
const data = performanceMonitor.exportData();
```

### Search Optimizer

```javascript
import { searchOptimizer } from './modules/search-optimizer.js';

// Perform optimized search
const results = await searchOptimizer.performOptimizedSearch(
  searchTerm, 
  conversations, 
  options
);

// Get cache statistics
const cacheStats = searchOptimizer.getCacheStats();

// Clear cache
searchOptimizer.clearCache();
```

### Progressive Search

```javascript
import { progressiveSearch } from './modules/progressive-search.js';

// Perform progressive search
const results = await progressiveSearch.performProgressiveSearch(
  searchTerm,
  conversations,
  onProgress,
  options
);

// Cancel search
progressiveSearch.cancelSearch(searchId);

// Get active search statistics
const stats = progressiveSearch.getActiveSearchStats();
```

### Performance Integration

```javascript
import { performanceIntegration } from './modules/performance-integration.js';

// Initialize with configuration
await performanceIntegration.initialize(container, {
  enableProgressiveSearch: true,
  enableSearchCache: true,
  enableAsyncDOM: true,
  enablePerformanceMonitoring: true
});

// Perform optimized search
const results = await performanceIntegration.performOptimizedSearch(
  searchTerm,
  conversations,
  onProgress,
  options
);

// Get performance statistics
const stats = performanceIntegration.getPerformanceStats();
```
