# LLMLog Development Setup Guide

**Version:** 0.1.0  
**Date:** August 17, 2025  
**Last Updated:** August 17, 2025

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Development Environment](#development-environment)
4. [Building the Extension](#building-the-extension)
5. [Testing](#testing)
6. [Debugging](#debugging)
7. [Code Style and Standards](#code-style-and-standards)
8. [Contributing](#contributing)

## Prerequisites

### Required Software

- **Node.js**: Version 16.0 or higher
- **npm**: Version 8.0 or higher (comes with Node.js)
- **Chrome Browser**: Latest version for testing
- **Git**: For version control

### Recommended Tools

- **Visual Studio Code**: With the following extensions:
  - ES6 String HTML
  - JavaScript (ES6) code snippets
  - Chrome Debugger
  - Jest Runner
- **Chrome DevTools**: For debugging and performance analysis

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/dandawong/llmlog.git
cd llmlog
```

### 2. Install Dependencies

```bash
npm install
```

This will install all required dependencies including:
- **Tailwind CSS**: For styling
- **Jest**: For testing
- **Babel**: For ES6 module support
- **PostCSS**: For CSS processing

### 3. Verify Installation

```bash
npm run test
```

This should run the test suite and display results.

## Development Environment

### Project Structure

```
llmlog/
├── manifest.json              # Extension manifest
├── popup.html                 # Popup interface
├── popup.js                   # Popup logic
├── service-worker.js          # Background service worker
├── options.html               # Options page
├── options.js                 # Options page logic
├── modules/                   # Core modules
│   ├── storage.js            # Data persistence
│   ├── router.js             # Message routing
│   ├── capture.js            # Platform configuration
│   ├── logger.js             # Logging system
│   ├── settings.js           # Settings management
│   ├── performance-*.js      # Performance optimization
│   └── ...
├── scripts/capture/          # Content scripts
│   ├── injector.js          # Script injection
│   ├── interceptor.js       # API interception
│   ├── bridge.js            # Communication bridge
│   └── platforms/           # Platform parsers
├── styles/                  # CSS source files
├── dist/                    # Built CSS files
├── test/                    # Test files
├── docs/                    # Documentation
└── lib/                     # Third-party libraries
```

### Environment Configuration

#### 1. Development Mode

Enable debug logging by setting the extension option:

```javascript
// In Chrome DevTools Console (on extension popup)
chrome.runtime.sendMessage({
  namespace: 'settings',
  action: 'set',
  payload: { key: 'debugLoggingEnabled', value: true }
});
```

#### 2. Performance Monitoring

The extension includes comprehensive performance monitoring. To enable:

```javascript
// Performance monitoring is enabled by default
// Check status in popup console:
console.log('Performance stats:', await performanceIntegration.getPerformanceStats());
```

## Building the Extension

### 1. Build CSS

```bash
# Build CSS once
npm run build

# Watch for changes and rebuild automatically
npm run watch
```

### 2. Load Extension in Chrome

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `llmlog` directory
5. The extension should now appear in your extensions list

### 3. Reload After Changes

After making code changes:
1. Click the refresh icon on the extension card in `chrome://extensions/`
2. Or use the keyboard shortcut `Ctrl+R` while on the extensions page

## Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests verbosely
npm run test:verbose
```

### Test Structure

```
test/
├── setup.js                 # Jest configuration and mocks
├── run-tests.js            # Test runner script
├── modules/                # Core module tests
│   ├── storage.test.js     # Storage module tests
│   ├── router.test.js      # Router module tests
│   └── ...
├── platforms/              # Platform parser tests
│   ├── chatgpt.test.js     # ChatGPT parser tests
│   ├── claude.test.js      # Claude parser tests
│   └── ...
└── performance/            # Performance tests
    └── memory-performance.test.js
```

### Writing Tests

#### Example Test Structure

```javascript
import { saveConversation } from '../modules/storage.js';

describe('Storage Module', () => {
  beforeEach(() => {
    // Setup test environment
  });

  afterEach(() => {
    // Cleanup after each test
  });

  test('should save conversation successfully', async () => {
    const conversationData = {
      platform: 'ChatGPT',
      title: 'Test conversation',
      prompt: 'Test prompt',
      response: 'Test response',
      url: 'https://chat.openai.com/test'
    };

    const result = await saveConversation(conversationData);
    
    expect(result.status).toBe('success');
    expect(result.data.id).toBeDefined();
  });
});
```

#### Test Best Practices

1. **Arrange-Act-Assert**: Structure tests clearly
2. **Mock External Dependencies**: Use Jest mocks for Chrome APIs
3. **Test Edge Cases**: Include error scenarios and boundary conditions
4. **Descriptive Names**: Use clear, descriptive test names
5. **Independent Tests**: Ensure tests don't depend on each other

## Debugging

### Chrome DevTools

#### 1. Service Worker Debugging

1. Go to `chrome://extensions/`
2. Find LLMLog extension
3. Click "service worker" link
4. DevTools will open for the service worker

#### 2. Content Script Debugging

1. Open the target website (e.g., ChatGPT)
2. Open DevTools (F12)
3. Go to Sources tab
4. Find "Content scripts" section
5. Locate LLMLog scripts

#### 3. Popup Debugging

1. Right-click the extension icon
2. Select "Inspect popup"
3. DevTools will open for the popup

### Logging

The extension includes comprehensive logging:

```javascript
// View logs in popup console
chrome.runtime.sendMessage({
  namespace: 'logging',
  action: 'getLogs'
}).then(response => {
  console.log('Extension logs:', response.data);
});
```

### Performance Debugging

```javascript
// Get performance statistics
performanceIntegration.getPerformanceStats().then(stats => {
  console.log('Performance stats:', stats);
});

// Export detailed performance data
performanceIntegration.exportPerformanceData().then(data => {
  console.log('Detailed performance data:', data);
});
```

## Code Style and Standards

### JavaScript Standards

- **ES6+ Modules**: Use import/export syntax
- **Async/Await**: Prefer async/await over Promises
- **Const/Let**: Use const by default, let when reassignment needed
- **Arrow Functions**: Use arrow functions for short functions
- **Template Literals**: Use template literals for string interpolation

### Code Organization

- **Single Responsibility**: Each module should have a single, well-defined purpose
- **Pure Functions**: Prefer pure functions when possible
- **Error Handling**: Always handle errors gracefully
- **Documentation**: Use JSDoc comments for functions and classes

### Example Code Style

```javascript
/**
 * Save conversation to IndexedDB
 * @param {Object} conversationData - Conversation data to save
 * @returns {Promise<Object>} Result object with status and data
 */
export async function saveConversation(conversationData) {
  try {
    // Validate input
    if (!conversationData || typeof conversationData !== 'object') {
      return { status: 'error', message: 'Invalid conversation data' };
    }

    // Perform operation
    const result = await performSave(conversationData);
    
    return { status: 'success', data: result };
  } catch (error) {
    console.error('Failed to save conversation:', error);
    return { status: 'error', message: error.message };
  }
}
```

### CSS Standards

- **Tailwind CSS**: Use Tailwind utility classes
- **Component-based**: Organize styles by component
- **Responsive**: Design mobile-first with responsive breakpoints
- **Accessibility**: Ensure proper contrast and focus states

## Contributing

### Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following style guidelines
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Changes**
   ```bash
   npm test
   npm run test:coverage
   ```

4. **Build and Test Extension**
   ```bash
   npm run build
   # Load extension in Chrome and test manually
   ```

5. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

6. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create pull request on GitHub
   ```

### Commit Message Format

Use conventional commit format:
- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation changes
- `test:` Test additions or modifications
- `refactor:` Code refactoring
- `perf:` Performance improvements
- `style:` Code style changes

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No console errors in extension
- [ ] Performance impact is acceptable
- [ ] Security considerations are addressed

## Troubleshooting

### Common Issues

#### 1. Extension Not Loading
- Check manifest.json syntax
- Verify all file paths are correct
- Check Chrome DevTools for errors

#### 2. Tests Failing
- Ensure all dependencies are installed
- Check Jest configuration in jest.config.js
- Verify mock setup in test/setup.js

#### 3. CSS Not Building
- Check Tailwind configuration
- Verify input/output paths
- Run `npm run build` manually

#### 4. Performance Issues
- Check performance monitoring data
- Review memory usage patterns
- Optimize search and DOM operations

### Getting Help

- **Documentation**: Check docs/ directory
- **Issues**: Create GitHub issue with detailed description
- **Debugging**: Use Chrome DevTools extensively
- **Testing**: Run test suite to identify problems

## Additional Resources

- [Chrome Extension Documentation](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 Migration Guide](https://developer.chrome.com/docs/extensions/mv3/intro/)
- [IndexedDB API](https://developer.mozilla.org/en-US/docs/Web/API/IndexedDB_API)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
