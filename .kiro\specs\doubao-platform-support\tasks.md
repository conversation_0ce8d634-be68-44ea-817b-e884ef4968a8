# Implementation Plan

- [x] 1. Create Doubao platform parser module





  - Create the platform parser file at `scripts/capture/platforms/doubao.js`
  - Implement the config object with name, apiEndpoint, and duplicateWindow properties
  - Export the configuration following the established pattern
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 2. Implement request parsing functionality



  - Write parseRequest function to extract user prompts from Doubao API requests
  - Handle the JSON structure with messages array and nested content
  - Parse the content field containing JSON string with text property
  - Add error handling for malformed request data
  - _Requirements: 1.2, 6.1, 6.3_

- [x] 3. Implement SSE response parsing functionality





  - Write parseResponse function to handle Server-Sent Events stream
  - Parse SSE event format with data lines and event structure
  - Extract event_data JSON and parse nested message content
  - Handle event_type filtering to process only message events (type 2001)
  - _Requirements: 1.2, 2.4, 4.1, 4.2_

- [x] 4. Implement delta response reconstruction





  - Build complete response text from incremental SSE delta updates
  - Concatenate text content from multiple SSE events
  - Handle is_delta and is_finish flags to determine completion
  - Extract conversation_id and message_id for duplicate detection
  - _Requirements: 4.1, 4.3, 4.4, 5.1, 5.2_

- [x] 5. Add comprehensive error handling and logging





  - Implement try-catch blocks for JSON parsing operations
  - Add detailed logging for debugging SSE stream processing
  - Handle malformed SSE events gracefully by skipping invalid chunks
  - Log parsing errors with context information for troubleshooting
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 6. Update capture module configuration


  - Add doubao entry to platformModulePaths object in modules/capture.js
  - Map 'doubao' platform identifier to the platform parser path
  - Ensure the configuration follows existing pattern for consistency
  - _Requirements: 2.1, 2.2_

- [x] 7. Update manifest.json permissions and resources


  - Add host permission for "https://www.doubao.com/*" to host_permissions array
  - Add content script match pattern for "https://www.doubao.com/*"
  - Include doubao.js in web_accessible_resources for the doubao.com domain
  - Update content_scripts matches array to include doubao.com
  - _Requirements: 7.1, 7.2, 7.3, 7.4_

- [x] 8. Implement conversation title generation


  - Create title generation logic from user prompt text
  - Truncate titles longer than 50 characters with ellipsis
  - Handle empty or missing prompts with default title format
  - Sanitize special characters for proper display in UI
  - _Requirements: 8.1, 8.2, 8.3, 8.4_

- [x] 9. Add duplicate detection logic


  - Implement primary duplicate detection using conversation_id and message_id
  - Add fallback duplicate detection using local_conversation_id and local_message_id
  - Set appropriate duplicate detection time window (30 seconds)
  - Integrate with existing duplicate prevention mechanisms
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 10. Create comprehensive unit tests for Doubao parser


  - Write tests for parseRequest function with valid and invalid request data
  - Create tests for parseResponse function with various SSE stream scenarios
  - Test delta reconstruction with multiple SSE events
  - Add tests for error handling and edge cases
  - _Requirements: 1.1, 1.2, 4.1, 6.1_

- [x] 11. Test platform integration end-to-end


  - Verify content script injection works on doubao.com
  - Test complete conversation capture flow from request to storage
  - Validate conversation display in popup UI with Doubao platform badge
  - Confirm search functionality includes Doubao conversations
  - _Requirements: 1.1, 1.3, 3.1, 3.2, 3.3, 3.4_

- [x] 12. Validate duplicate prevention functionality



  - Test that identical conversations are not captured multiple times
  - Verify duplicate detection works with both ID-based and content-based methods
  - Confirm time window duplicate prevention operates correctly
  - Test edge cases where duplicate detection might fail
  - _Requirements: 5.1, 5.2, 5.3, 5.4_