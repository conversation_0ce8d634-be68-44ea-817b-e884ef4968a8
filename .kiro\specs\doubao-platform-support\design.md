# Design Document

## Overview

This design document outlines the implementation approach for adding Doubao (豆包) platform support to the LLMLog Chrome extension. The design follows the established patterns used for other AI platforms while accommodating Doubao's unique SSE streaming response format and API structure.

Doubao uses a Server-Sent Events (SSE) streaming API with a specific JSON structure that requires careful parsing to extract conversation data. The implementation will integrate seamlessly with the existing LLMLog architecture.

## Architecture

### Platform Integration Architecture

The Doubao integration follows the existing platform parser pattern:

```
Content Script (doubao.com)
    ↓
Interceptor (fetch override)
    ↓
Doubao Platform Parser
    ↓
Bridge Communication
    ↓
Service Worker
    ↓
Storage Module
```

### API Endpoint Analysis

Based on the provided information, Doubao uses:
- **Endpoint**: `/samantha/chat/completion` with various query parameters
- **Method**: POST with JSON payload
- **Response**: Server-Sent Events (SSE) stream
- **Content Type**: `application/json` for requests, `text/event-stream` for responses

## Components and Interfaces

### 1. Platform Parser (`scripts/capture/platforms/doubao.js`)

#### Configuration Object
```javascript
export const config = {
    name: '<PERSON><PERSON><PERSON>',
    apiEndpoint: '/samantha/chat/completion',
    duplicateWindow: 30000 // 30 seconds
};
```

#### Request Parser Function
```javascript
export async function parseRequest(request, logger) {
    // Extract user prompt from request payload
    // Handle JSON structure: messages[0].content.text
    // Return the user's input text
}
```

#### Response Parser Function
```javascript
export async function parseResponse(response, logger) {
    // Parse SSE stream
    // Extract text content from event_data JSON
    // Reconstruct complete response from delta chunks
    // Return { text: string, id: string }
}
```

### 2. SSE Stream Processing

#### Event Data Structure
Doubao SSE events contain:
- `event_type`: Type of event (2001 for message content)
- `event_data`: JSON string containing message data
- `event_id`: Sequential event identifier

#### Message Content Structure
Within `event_data`:
- `message.content`: JSON string containing `{"text": "content"}`
- `conversation_id`: Unique conversation identifier
- `local_conversation_id`: Local conversation identifier
- `message_id`: Unique message identifier
- `is_delta`: Boolean indicating incremental update
- `is_finish`: Boolean indicating completion

### 3. Content Reconstruction Logic

#### Delta Processing
1. Parse each SSE event
2. Extract text content from JSON structure
3. Concatenate delta updates to build complete response
4. Handle completion markers (`is_finish: true`)

#### Error Handling
- Skip malformed JSON chunks
- Continue processing on parse errors
- Log detailed error information for debugging

## Data Models

### Request Data Model
```javascript
{
  messages: [{
    content: '{"text":"user input"}',
    content_type: 2001,
    attachments: [],
    references: []
  }],
  completion_option: {
    is_regen: false,
    with_suggest: true,
    need_create_conversation: true,
    // ... other options
  },
  conversation_id: "string",
  local_conversation_id: "string",
  local_message_id: "string"
}
```

### Response Data Model
```javascript
// SSE Event Structure
{
  event_data: "JSON string",
  event_id: "string",
  event_type: number
}

// Parsed Event Data
{
  message: {
    content_type: 2001,
    content: '{"text":"response text"}',
    id: "string"
  },
  message_id: "string",
  conversation_id: "string",
  local_conversation_id: "string",
  is_delta: boolean,
  is_finish: boolean,
  status: number
}
```

### Conversation Storage Model
```javascript
{
  platform: 'Doubao',
  title: 'Generated from user prompt',
  prompt: 'User input text',
  response: 'Complete AI response',
  url: 'https://www.doubao.com/chat/{conversation_id}',
  conversationId: 'conversation_id from API',
  messageId: 'message_id from API',
  createdAt: timestamp
}
```

## Error Handling

### Request Parsing Errors
- Log parsing failures with request details
- Return empty string as fallback
- Continue processing without blocking

### Response Parsing Errors
- Skip malformed SSE chunks
- Log detailed error information
- Attempt to continue with partial data
- Return accumulated text even if incomplete

### Network Errors
- Handle fetch interception failures
- Provide graceful degradation
- Log network-related issues

### JSON Parsing Errors
- Handle malformed JSON in SSE events
- Skip invalid chunks and continue
- Log specific parsing errors for debugging

## Testing Strategy

### Unit Testing
1. **Request Parser Tests**
   - Valid request payload parsing
   - Malformed request handling
   - Empty request scenarios

2. **Response Parser Tests**
   - Complete SSE stream processing
   - Partial stream handling
   - Malformed event handling
   - Delta reconstruction accuracy

3. **Content Extraction Tests**
   - JSON content parsing
   - Text concatenation
   - Special character handling

### Integration Testing
1. **End-to-End Conversation Capture**
   - Complete conversation flow
   - Multiple conversation handling
   - Duplicate detection

2. **Platform Integration Tests**
   - Content script injection
   - Bridge communication
   - Storage integration

### Manual Testing
1. **Live Platform Testing**
   - Real conversations on doubao.com
   - Various conversation types
   - Error scenario testing

2. **UI Integration Testing**
   - Conversation list display
   - Platform badge appearance
   - Search functionality

## Implementation Details

### SSE Stream Processing Algorithm

```javascript
function processSSEStream(sseStream, logger) {
  const events = sseStream.split('\n\n').filter(Boolean);
  let fullText = '';
  let conversationId = null;
  let messageId = null;
  
  for (const eventBlock of events) {
    const lines = eventBlock.split('\n');
    let dataLine = lines.find(line => line.startsWith('data:'));
    
    if (!dataLine) continue;
    
    const dataString = dataLine.substring(5).trim();
    
    try {
      const eventData = JSON.parse(dataString);
      
      if (eventData.event_type === 2001) {
        const messageData = JSON.parse(eventData.event_data);
        
        if (messageData.message && messageData.message.content) {
          const contentData = JSON.parse(messageData.message.content);
          
          if (contentData.text) {
            fullText += contentData.text;
          }
        }
        
        // Extract IDs for duplicate detection
        if (messageData.conversation_id) {
          conversationId = messageData.conversation_id;
        }
        if (messageData.message_id) {
          messageId = messageData.message_id;
        }
      }
    } catch (error) {
      logger.warn('Failed to parse SSE event:', error);
    }
  }
  
  return { text: fullText, id: conversationId, messageId };
}
```

### Duplicate Detection Strategy

1. **Primary**: Use `conversation_id` + `message_id` combination
2. **Secondary**: Use `local_conversation_id` + `local_message_id`
3. **Fallback**: Content-based detection using URL + prompt + response
4. **Time Window**: 30 seconds for Doubao platform

### URL Construction

Generate conversation URLs using the pattern:
`https://www.doubao.com/chat/{conversation_id}`

### Title Generation

1. Extract first 50 characters from user prompt
2. Remove special characters and normalize whitespace
3. Add ellipsis if truncated
4. Use "Doubao Conversation" as fallback

## Security Considerations

### Content Security Policy
- Ensure platform parser is included in web accessible resources
- Maintain CSP compliance for script injection

### Data Privacy
- All processing remains local (consistent with existing platforms)
- No external data transmission
- Secure handling of conversation content

### Script Injection Security
- Use existing secure injection mechanisms
- Validate all injected scripts
- Follow established security patterns

## Performance Considerations

### Stream Processing Optimization
- Process SSE events incrementally
- Avoid blocking on large responses
- Implement efficient string concatenation

### Memory Management
- Clean up event listeners
- Release resources after processing
- Avoid memory leaks in long conversations

### Caching Strategy
- Leverage existing search cache mechanisms
- No additional caching required for Doubao-specific features

## Configuration Updates Required

### Manifest.json Updates
1. Add host permission: `"https://www.doubao.com/*"`
2. Add content script match: `"https://www.doubao.com/*"`
3. Add web accessible resource: `"scripts/capture/platforms/doubao.js"`

### Capture Module Updates
1. Add doubao entry to `platformModulePaths`
2. Map 'doubao' to platform parser path

### Platform Detection
- Detect Doubao platform based on hostname: `www.doubao.com`
- Use existing platform detection mechanisms

## Deployment Considerations

### Rollout Strategy
1. Deploy with feature flag capability
2. Monitor for parsing errors
3. Gradual rollout to users

### Monitoring
- Track parsing success rates
- Monitor error logs for issues
- Collect performance metrics

### Maintenance
- Regular testing against platform changes
- Update parsing logic as needed
- Maintain compatibility with API updates