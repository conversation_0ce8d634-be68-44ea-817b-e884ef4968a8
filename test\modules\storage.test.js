/**
 * Unit Tests for Storage Module
 * 
 * Tests the IndexedDB storage functionality with mock database operations.
 */

import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock IndexedDB
import 'fake-indexeddb/auto';
import FDBFactory from 'fake-indexeddb/lib/FDBFactory';

// Mock chrome APIs that might be used indirectly
global.chrome = {
  runtime: {
    lastError: null
  }
};

describe('Storage Module', () => {
  let storageModule;
  
  beforeEach(async () => {
    // Reset IndexedDB
    indexedDB = new FDBFactory();
    
    // Clear module cache and re-import
    jest.resetModules();
    storageModule = await import('../../modules/storage.js');
  });

  afterEach(async () => {
    // Close database connection after each test
    if (storageModule.closeDB) {
      await storageModule.closeDB();
    }
  });

  describe('saveConversation', () => {
    test('should save a new conversation', async () => {
      const conversationData = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation'
      };
      
      const result = await storageModule.saveConversation(conversationData);
      
      expect(result.status).toBe('success');
      expect(result.data.id).toBeDefined();
      expect(result.data.duplicate).toBeUndefined();
    });

    test('should detect and skip duplicate conversations', async () => {
      const conversationData = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation'
      };
      
      // Save first conversation
      const result1 = await storageModule.saveConversation(conversationData);
      expect(result1.status).toBe('success');
      
      // Try to save duplicate conversation
      const result2 = await storageModule.saveConversation(conversationData);
      
      expect(result2.status).toBe('success');
      expect(result2.data.id).toBe(result1.data.id);
      expect(result2.data.duplicate).toBe(true);
    });

    test('should handle database errors when saving', async () => {
      // Mock getDB to throw an error
      jest.mock('../../modules/storage.js', () => ({
        ...jest.requireActual('../../modules/storage.js'),
        getDB: jest.fn().mockRejectedValue(new Error('Database error'))
      }));
      
      // Re-import the module with the mock
      jest.resetModules();
      const storageModuleWithMock = await import('../../modules/storage.js');
      
      const conversationData = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation'
      };
      
      const result = await storageModuleWithMock.saveConversation(conversationData);
      
      expect(result.status).toBe('error');
      expect(result.message).toBe('Database error');
    });
  });

  describe('getAllConversations', () => {
    test('should retrieve all conversations sorted by creation date', async () => {
      // Save a few conversations
      const conversation1 = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test1',
        createdAt: new Date(Date.now() - 10000).toISOString(),
        title: 'Test Conversation 1'
      };
      
      const conversation2 = {
        platform: 'Claude',
        prompt: 'How are you?',
        response: 'I am fine, thank you!',
        url: 'https://claude.ai/chat/test2',
        createdAt: new Date(Date.now() - 5000).toISOString(),
        title: 'Test Conversation 2'
      };
      
      const result1 = await storageModule.saveConversation(conversation1);
      const result2 = await storageModule.saveConversation(conversation2);
      
      expect(result1.status).toBe('success');
      expect(result2.status).toBe('success');
      
      // Retrieve all conversations
      const result = await storageModule.getAllConversations();
      
      expect(result.status).toBe('success');
      expect(result.data.length).toBe(2);
      // Should be sorted by creation date (newest first)
      expect(result.data[0].id).toBe(result2.data.id);
      expect(result.data[1].id).toBe(result1.data.id);
    });

    test('should handle database errors when retrieving all conversations', async () => {
      // Mock getDB to throw an error
      jest.mock('../../modules/storage.js', () => ({
        ...jest.requireActual('../../modules/storage.js'),
        getDB: jest.fn().mockRejectedValue(new Error('Database error'))
      }));
      
      // Re-import the module with the mock
      jest.resetModules();
      const storageModuleWithMock = await import('../../modules/storage.js');
      
      const result = await storageModuleWithMock.getAllConversations();
      
      expect(result.status).toBe('error');
      expect(result.message).toBe('Database error');
    });
  });

  describe('getConversations', () => {
    test('should retrieve conversations with pagination', async () => {
      // Save multiple conversations
      const conversations = [];
      for (let i = 0; i < 10; i++) {
        const conversation = {
          platform: 'ChatGPT',
          prompt: `Hello ${i}`,
          response: `Hi there ${i}!`,
          url: `https://chat.openai.com/c/test${i}`,
          createdAt: new Date(Date.now() - (10 - i) * 1000).toISOString(),
          title: `Test Conversation ${i}`
        };
        const result = await storageModule.saveConversation(conversation);
        expect(result.status).toBe('success');
        conversations.push({ ...conversation, id: result.data.id });
      }
      
      // Retrieve first page
      const result1 = await storageModule.getConversations({ page: 1, limit: 5 });
      
      expect(result1.status).toBe('success');
      expect(result1.data.length).toBe(5);
      expect(result1.pagination.page).toBe(1);
      expect(result1.pagination.limit).toBe(5);
      expect(result1.pagination.hasMore).toBe(true);
      
      // Retrieve second page
      const result2 = await storageModule.getConversations({ page: 2, limit: 5 });
      
      expect(result2.status).toBe('success');
      expect(result2.data.length).toBe(5);
      expect(result2.pagination.page).toBe(2);
      expect(result2.pagination.limit).toBe(5);
      expect(result2.pagination.hasMore).toBe(false);
    });

    test('should filter conversations by platform', async () => {
      // Save conversations for different platforms
      const chatgptConversation = {
        platform: 'ChatGPT',
        prompt: 'Hello ChatGPT',
        response: 'Hi from ChatGPT!',
        url: 'https://chat.openai.com/c/test1',
        createdAt: new Date().toISOString(),
        title: 'ChatGPT Conversation'
      };
      
      const claudeConversation = {
        platform: 'Claude',
        prompt: 'Hello Claude',
        response: 'Hi from Claude!',
        url: 'https://claude.ai/chat/test2',
        createdAt: new Date().toISOString(),
        title: 'Claude Conversation'
      };
      
      await storageModule.saveConversation(chatgptConversation);
      await storageModule.saveConversation(claudeConversation);
      
      // Retrieve only ChatGPT conversations
      const result = await storageModule.getConversations({ platform: 'ChatGPT' });
      
      expect(result.status).toBe('success');
      expect(result.data.length).toBe(1);
      expect(result.data[0].platform).toBe('ChatGPT');
    });

    test('should search conversations by term', async () => {
      // Save conversations with different content
      const conversation1 = {
        platform: 'ChatGPT',
        prompt: 'What is artificial intelligence?',
        response: 'Artificial intelligence is a branch of computer science.',
        url: 'https://chat.openai.com/c/test1',
        createdAt: new Date().toISOString(),
        title: 'AI Discussion'
      };
      
      const conversation2 = {
        platform: 'Claude',
        prompt: 'How does machine learning work?',
        response: 'Machine learning is a subset of AI.',
        url: 'https://claude.ai/chat/test2',
        createdAt: new Date().toISOString(),
        title: 'ML Explanation'
      };
      
      await storageModule.saveConversation(conversation1);
      await storageModule.saveConversation(conversation2);
      
      // Search for conversations containing "artificial"
      const result = await storageModule.getConversations({ search: 'artificial' });
      
      expect(result.status).toBe('success');
      expect(result.data.length).toBe(1);
      expect(result.data[0].prompt).toContain('artificial');
    });
  });

  describe('getTotalConversationCount', () => {
    test('should return total count of conversations', async () => {
      // Save a few conversations
      const conversation1 = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test1',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation 1'
      };
      
      const conversation2 = {
        platform: 'Claude',
        prompt: 'How are you?',
        response: 'I am fine, thank you!',
        url: 'https://claude.ai/chat/test2',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation 2'
      };
      
      await storageModule.saveConversation(conversation1);
      await storageModule.saveConversation(conversation2);
      
      // Get total count
      const result = await storageModule.getTotalConversationCount();
      
      expect(result.status).toBe('success');
      expect(result.data.totalCount).toBe(2);
    });

    test('should return filtered count when search term is provided', async () => {
      // Save conversations with different content
      const conversation1 = {
        platform: 'ChatGPT',
        prompt: 'What is artificial intelligence?',
        response: 'Artificial intelligence is a branch of computer science.',
        url: 'https://chat.openai.com/c/test1',
        createdAt: new Date().toISOString(),
        title: 'AI Discussion'
      };
      
      const conversation2 = {
        platform: 'Claude',
        prompt: 'How does machine learning work?',
        response: 'Machine learning is a subset of AI.',
        url: 'https://claude.ai/chat/test2',
        createdAt: new Date().toISOString(),
        title: 'ML Explanation'
      };
      
      await storageModule.saveConversation(conversation1);
      await storageModule.saveConversation(conversation2);
      
      // Get count of conversations containing "artificial"
      const result = await storageModule.getTotalConversationCount('artificial');
      
      expect(result.status).toBe('success');
      expect(result.data.totalCount).toBe(1);
    });
  });

  describe('deleteConversation', () => {
    test('should delete a conversation by ID', async () => {
      // Save a conversation
      const conversationData = {
        platform: 'ChatGPT',
        prompt: 'Hello',
        response: 'Hi there!',
        url: 'https://chat.openai.com/c/test',
        createdAt: new Date().toISOString(),
        title: 'Test Conversation'
      };
      
      const saveResult = await storageModule.saveConversation(conversationData);
      expect(saveResult.status).toBe('success');
      
      // Delete the conversation
      const deleteResult = await storageModule.deleteConversation({ id: saveResult.data.id });
      
      expect(deleteResult.status).toBe('success');
      expect(deleteResult.data.id).toBe(saveResult.data.id);
      
      // Verify the conversation is deleted
      const getResult = await storageModule.getAllConversations();
      expect(getResult.status).toBe('success');
      expect(getResult.data.length).toBe(0);
    });

    test('should handle database errors when deleting', async () => {
      // Mock getDB to throw an error
      jest.mock('../../modules/storage.js', () => ({
        ...jest.requireActual('../../modules/storage.js'),
        getDB: jest.fn().mockRejectedValue(new Error('Database error'))
      }));
      
      // Re-import the module with the mock
      jest.resetModules();
      const storageModuleWithMock = await import('../../modules/storage.js');
      
      const result = await storageModuleWithMock.deleteConversation({ id: 1 });
      
      expect(result.status).toBe('error');
      expect(result.message).toBe('Database error');
    });
  });
});