# LLMLog User Guide

**Version:** 0.1.0  
**Date:** August 17, 2025  
**Last Updated:** August 17, 2025

## Table of Contents

1. [Getting Started](#getting-started)
2. [Features Overview](#features-overview)
3. [Using LLMLog](#using-llmlog)
4. [Search and Filtering](#search-and-filtering)
5. [Managing Conversations](#managing-conversations)
6. [Settings and Configuration](#settings-and-configuration)
7. [Supported Platforms](#supported-platforms)
8. [Privacy and Security](#privacy-and-security)
9. [Troubleshooting](#troubleshooting)
10. [FAQ](#faq)

## Getting Started

### What is LLMLog?

LLMLog is a Chrome extension that automatically captures and organizes your conversations with AI language models. It works seamlessly with popular platforms like ChatGPT, Claude, Gemini, and others, creating a personal knowledge base of your AI interactions.

### Installation

1. **Download the Extension**
   - Get LLMLog from the Chrome Web Store (coming soon)
   - Or install from source (see Development Setup guide)

2. **Install in Chrome**
   - Click "Add to Chrome"
   - Confirm installation when prompted
   - The LLMLog icon will appear in your browser toolbar

3. **Initial Setup**
   - Click the LLMLog icon to open the popup
   - The extension will automatically start capturing conversations
   - No additional configuration required!

### First Use

1. **Visit a Supported Platform**
   - Go to ChatGPT, Claude, or another supported platform
   - Start a conversation as you normally would

2. **Automatic Capture**
   - LLMLog automatically detects and saves conversations
   - No manual action required

3. **View Your Conversations**
   - Click the LLMLog icon in your toolbar
   - Browse your captured conversations
   - Use search to find specific topics

## Features Overview

### 🤖 Automatic Conversation Capture
- **Real-time Detection**: Captures conversations as they happen
- **Multiple Platforms**: Works with 6 major AI platforms
- **Smart Duplicate Prevention**: Avoids saving duplicate conversations
- **Conversation Threading**: Maintains conversation context and flow

### 🔍 Advanced Search
- **Full-text Search**: Search across all conversation content
- **Relevance Scoring**: Results ranked by relevance and recency
- **Platform Filtering**: Filter by specific AI platforms
- **Instant Results**: Fast search with progressive loading

### 📊 Conversation Management
- **List and Detail Views**: Browse conversations efficiently
- **Date Sorting**: Conversations sorted by creation date
- **Platform Indicators**: Visual platform identification
- **Easy Deletion**: Remove unwanted conversations

### 🚀 Performance Optimized
- **Fast Loading**: Optimized for large conversation collections
- **Smooth Scrolling**: Virtual scrolling for thousands of conversations
- **Intelligent Caching**: Faster repeat searches
- **Memory Efficient**: Minimal resource usage

### 🔒 Privacy Focused
- **Local Storage Only**: All data stays on your device
- **No External Transmission**: Zero data sent to external servers
- **Secure by Design**: Built with privacy as a core principle

## Using LLMLog

### Opening LLMLog

Click the LLMLog icon in your Chrome toolbar to open the main interface.

### Main Interface

The LLMLog popup consists of:

1. **Header**
   - Search bar for finding conversations
   - Settings button (gear icon)

2. **Conversation List**
   - Shows all captured conversations
   - Each item displays:
     - Platform badge (colored indicator)
     - Conversation title
     - Date and time
     - Preview of the conversation

3. **Navigation**
   - Back button (when viewing conversation details)
   - Load more button (for pagination)

### Viewing Conversations

1. **List View** (Default)
   - Scroll through your conversations
   - Click any conversation to view details

2. **Detail View**
   - Shows full conversation content
   - Displays both your prompt and AI response
   - Formatted with syntax highlighting
   - Click "Back" to return to list

### Automatic Capture Process

LLMLog automatically captures conversations when you:

1. **Ask a Question**: Type and submit a prompt to an AI
2. **Receive a Response**: The AI provides a response
3. **Automatic Save**: LLMLog detects and saves the conversation
4. **Instant Availability**: Conversation appears in your list immediately

## Search and Filtering

### Basic Search

1. **Enter Search Terms**
   - Type in the search bar at the top
   - Search is case-insensitive
   - Results appear as you type

2. **Search Scope**
   - Searches conversation titles
   - Searches your prompts/questions
   - Searches AI responses
   - Searches platform names

### Advanced Search Features

#### Multi-term Search
- Use multiple words: `artificial intelligence machine learning`
- All terms must be present (AND logic)
- Order doesn't matter

#### Platform-specific Search
- Filter by platform using the platform selector
- Combine with text search for precise results

#### Search Tips
- **Be Specific**: More specific terms yield better results
- **Use Key Concepts**: Search for main topics or concepts
- **Try Variations**: Use different phrasings if needed
- **Recent Boost**: Recent conversations rank higher in results

### Search Performance

- **Instant Results**: Search results appear immediately
- **Progressive Loading**: Results stream in for large datasets
- **Cached Results**: Repeated searches are lightning fast
- **Relevance Ranking**: Most relevant results appear first

## Managing Conversations

### Deleting Conversations

1. **From List View**
   - Click the delete button (trash icon) on any conversation
   - Confirm deletion when prompted

2. **From Detail View**
   - Click the delete button in the conversation detail
   - Confirm deletion when prompted

**Note**: Deleted conversations cannot be recovered.

### Conversation Organization

Conversations are automatically organized by:
- **Date**: Newest conversations appear first
- **Platform**: Visual indicators show the source platform
- **Relevance**: Search results ranked by relevance

### Export Options

Currently, LLMLog stores conversations locally. Export features are planned for future releases.

## Settings and Configuration

### Accessing Settings

Click the gear icon in the LLMLog popup header to access settings.

### Available Settings

#### Debug Logging
- **Purpose**: Enable detailed logging for troubleshooting
- **Default**: Disabled
- **When to Enable**: Only when experiencing issues

#### Capture Settings
- **Purpose**: Control which platforms capture conversations
- **Default**: All platforms enabled
- **Customization**: Enable/disable specific platforms

### Performance Settings

LLMLog automatically optimizes performance, but you can monitor:
- **Memory Usage**: Check extension memory consumption
- **Cache Status**: View search cache efficiency
- **Performance Metrics**: See operation timing statistics

## Supported Platforms

### Fully Supported Platforms

#### 1. **ChatGPT** (chat.openai.com)
- ✅ Real-time conversation capture
- ✅ Conversation threading
- ✅ Duplicate detection
- ✅ Full content preservation

#### 2. **Claude** (claude.ai)
- ✅ Message parsing and threading
- ✅ Content part concatenation
- ✅ Conversation context preservation
- ✅ Duplicate prevention

#### 3. **Google Gemini** (gemini.google.com)
- ✅ Form data parsing
- ✅ Streaming response handling
- ✅ Multi-chunk assembly
- ✅ Error handling

#### 4. **DeepSeek** (chat.deepseek.com)
- ✅ SSE stream parsing
- ✅ Session management
- ✅ Conversation capture
- ✅ Response formatting

#### 5. **Tongyi Qianwen** (tongyi.com)
- ✅ Dialog conversation parsing
- ✅ Content extraction
- ✅ Response handling
- ✅ Platform integration

#### 6. **Kimi** (kimi.com)
- ✅ Chat completion parsing
- ✅ Message threading
- ✅ Content preservation
- ✅ Duplicate detection

### Platform-Specific Features

Each platform may have unique characteristics:
- **Response Formats**: Different platforms use different data structures
- **Conversation IDs**: Platform-specific identification methods
- **Duplicate Windows**: Different time windows for duplicate detection
- **Content Types**: Various content formats (text, code, images)

## Privacy and Security

### Data Privacy

#### Local Storage Only
- **All conversations stored locally** on your device
- **No external servers** involved in data storage
- **No data transmission** to LLMLog servers or third parties
- **Complete privacy** of your AI conversations

#### Data Security
- **Browser-level encryption** for IndexedDB storage
- **Secure script injection** with validation
- **Content Security Policy** enforcement
- **XSS protection** for all user inputs

### Permissions

LLMLog requests minimal permissions:
- **Storage**: To save conversations locally
- **Active Tab**: To detect current page
- **Host Permissions**: Only for supported AI platforms
- **Scripting**: To capture conversations

### Data Retention

- **Indefinite Storage**: Conversations stored until manually deleted
- **No Automatic Deletion**: You control what gets deleted
- **Local Control**: All data management happens locally

## Troubleshooting

### Common Issues

#### Extension Not Capturing Conversations

**Possible Causes:**
- Extension not enabled
- Platform not supported
- Browser blocking content scripts

**Solutions:**
1. Check extension is enabled in `chrome://extensions/`
2. Refresh the AI platform page
3. Check if platform is in supported list
4. Try disabling other extensions temporarily

#### Search Not Working

**Possible Causes:**
- No conversations saved yet
- Search terms too specific
- Performance issues

**Solutions:**
1. Verify conversations are being captured
2. Try broader search terms
3. Clear search cache in settings
4. Restart browser if needed

#### Performance Issues

**Possible Causes:**
- Large number of conversations
- Memory constraints
- Other extensions interfering

**Solutions:**
1. Check performance statistics in settings
2. Clear browser cache
3. Restart Chrome
4. Delete old conversations if needed

### Getting Help

#### Debug Information
1. Enable debug logging in settings
2. Reproduce the issue
3. Check browser console for errors
4. Note specific error messages

#### Reporting Issues
When reporting issues, include:
- Chrome version
- Extension version
- Platform where issue occurred
- Steps to reproduce
- Any error messages

## FAQ

### General Questions

**Q: Is LLMLog free to use?**
A: Yes, LLMLog is completely free and open-source.

**Q: Does LLMLog work offline?**
A: Yes, once conversations are captured, you can browse and search them offline.

**Q: Can I export my conversations?**
A: Export features are planned for future releases.

**Q: How much storage does LLMLog use?**
A: Storage usage depends on conversation volume. The extension is optimized for efficiency.

### Privacy Questions

**Q: Where is my data stored?**
A: All data is stored locally in your browser using IndexedDB. Nothing is sent to external servers.

**Q: Can LLMLog see my conversations on other devices?**
A: No, conversations are only stored locally on the device where they were captured.

**Q: Does LLMLog track my browsing?**
A: No, LLMLog only monitors supported AI platforms and only captures conversation data.

### Technical Questions

**Q: Which browsers are supported?**
A: Currently only Chrome and Chromium-based browsers are supported.

**Q: Can I use LLMLog on mobile?**
A: Not currently, as it's a Chrome extension designed for desktop browsers.

**Q: How do I backup my conversations?**
A: Currently, conversations are stored in browser storage. Backup features are planned for future releases.

**Q: What happens if I uninstall the extension?**
A: All stored conversations will be deleted when the extension is uninstalled.

### Platform Questions

**Q: Why isn't my favorite AI platform supported?**
A: We're continuously adding support for new platforms. Check our roadmap for planned additions.

**Q: Can I request support for a new platform?**
A: Yes! Please create an issue on our GitHub repository with platform details.

**Q: Do all platforms capture the same information?**
A: Core information (prompt, response, date) is captured for all platforms, but some platform-specific features may vary.

## Support and Feedback

### Getting Support
- **Documentation**: Check this user guide and API documentation
- **GitHub Issues**: Report bugs and request features
- **Community**: Join discussions about LLMLog usage

### Providing Feedback
We welcome feedback on:
- Feature requests
- User experience improvements
- Bug reports
- Platform support requests

### Contributing
LLMLog is open-source! Contributions are welcome:
- Code contributions
- Documentation improvements
- Testing and bug reports
- Feature suggestions

---

**Thank you for using LLMLog!** We hope it helps you organize and leverage your AI conversations effectively.
