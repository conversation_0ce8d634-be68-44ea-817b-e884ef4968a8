/**
 * Centralized Logger Module
 *
 * Provides a conditional logger that can be enabled or disabled for debugging.
 */

// This is a factory function because the logger needs to be instantiated
// differently in different script contexts (some don't have access to chrome.* APIs).
export function createLogger(initialDebugMode) {
    let debugMode = initialDebugMode;
    
    return {
        setDebugMode(mode) {
            debugMode = mode;
        },
        log: (...args) => {
            if (debugMode) {
                console.log('LLMLog:', ...args);
            }
        },
        error: (...args) => {
            // Errors should probably always be logged, but we'll respect the flag for now.
            if (debugMode) {
                console.error('LLMLog:', ...args);
            }
        },
        warn: (...args) => {
            if (debugMode) {
                console.warn('LLMLog:', ...args);
            }
        },
        info: (...args) => {
            if (debugMode) {
                console.info('LLMLog:', ...args);
            }
        }
    };
}
